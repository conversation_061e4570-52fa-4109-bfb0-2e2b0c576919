using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    public class LogParser
    {
        private string[] _fieldNames;

        public async Task<List<LogEntry>> ParseLogFileAsync(string filePath, IProgress<int> progress = null)
        {
            var entries = new List<LogEntry>();
            var lines = await File.ReadAllLinesAsync(filePath);
            var totalLines = lines.Length;
            
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                
                if (line.StartsWith("#Fields:"))
                {
                    _fieldNames = line.Substring(8).Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    continue;
                }
                
                if (line.StartsWith("#") || string.IsNullOrWhiteSpace(line))
                    continue;

                var entry = ParseLogLine(line);
                if (entry != null)
                    entries.Add(entry);

                progress?.Report((i * 100) / totalLines);
            }

            return entries;
        }

        private LogEntry ParseLogLine(string line)
        {
            try
            {
                var fields = line.Split(' ');
                if (_fieldNames == null) return null;

                var entry = new LogEntry();
                
                for (int i = 0; i < Math.Min(fields.Length, _fieldNames.Length); i++)
                {
                    var fieldName = _fieldNames[i];
                    var fieldValue = fields[i];
                    
                    switch (fieldName)
                    {
                        case "date":
                            if (i + 1 < fields.Length && _fieldNames.Length > i + 1 && _fieldNames[i + 1] == "time")
                            {
                                if (DateTime.TryParseExact($"{fieldValue} {fields[i + 1]}", "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dt))
                                    entry.DateTime = dt;
                            }
                            break;
                        case "c-ip":
                            entry.ClientIP = fieldValue == "-" ? "" : fieldValue;
                            break;
                        case "cs-method":
                            entry.Method = fieldValue == "-" ? "" : fieldValue;
                            break;
                        case "cs-uri-stem":
                            entry.UriStem = fieldValue == "-" ? "" : fieldValue;
                            break;
                        case "cs-uri-query":
                            entry.UriQuery = fieldValue == "-" ? "" : fieldValue;
                            break;
                        case "sc-status":
                            if (int.TryParse(fieldValue, out int status))
                                entry.StatusCode = status;
                            break;
                        case "sc-bytes":
                            if (long.TryParse(fieldValue, out long bytesSent))
                                entry.BytesSent = bytesSent;
                            break;
                        case "cs-bytes":
                            if (long.TryParse(fieldValue, out long bytesReceived))
                                entry.BytesReceived = bytesReceived;
                            break;
                        case "time-taken":
                            if (int.TryParse(fieldValue, out int timeTaken))
                                entry.TimeTaken = timeTaken;
                            break;
                        case "cs(User-Agent)":
                            entry.UserAgent = fieldValue == "-" ? "" : fieldValue.Replace("+", " ");
                            break;
                        case "cs(Referer)":
                            entry.Referer = fieldValue == "-" ? "" : fieldValue;
                            break;
                        case "cs-username":
                            entry.Username = fieldValue == "-" ? "" : fieldValue;
                            break;
                    }
                }
                
                // Return entry if we have at least date/time and method
                return entry.DateTime != default && !string.IsNullOrEmpty(entry.Method) ? entry : null;
            }
            catch
            {
                return null;
            }
        }
    }
}