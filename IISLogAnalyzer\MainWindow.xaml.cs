using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using LiveCharts;
using LiveCharts.Wpf;
using Microsoft.Win32;
using IISLogAnalyzer.Models;
using IISLogAnalyzer.Services;

namespace IISLogAnalyzer
{
    public partial class MainWindow : Window
    {
        private readonly LogParser _logParser;
        private readonly AnalysisService _analysisService;
        private readonly ExportService _exportService;
        private readonly RealtimeMonitor _realtimeMonitor;
        private readonly AlertService _alertService;
        private List<LogEntry> _logEntries;
        private List<LogEntry> _filteredEntries;
        private Dictionary<string, object> _analysisResults;
        private System.Windows.Threading.DispatcherTimer _alertTimer;

        public MainWindow()
        {
            InitializeComponent();
            _logParser = new LogParser();
            _analysisService = new AnalysisService();
            _exportService = new ExportService();
            _realtimeMonitor = new RealtimeMonitor();
            _alertService = new AlertService();
            _analysisResults = new Dictionary<string, object>();
            
            _alertTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1)
            };
            _alertTimer.Tick += AlertTimer_Tick;
            
            _realtimeMonitor.NewEntriesDetected += OnNewEntriesDetected;
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Log files (*.log)|*.log|All files (*.*)|*.*",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                FilePathTextBox.Text = openFileDialog.FileName;
                AnalyzeButton.IsEnabled = true;
            }
        }

        private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(FilePathTextBox.Text) || !File.Exists(FilePathTextBox.Text))
            {
                MessageBox.Show("Please select a valid log file.");
                return;
            }

            ProgressBar.Visibility = Visibility.Visible;
            AnalyzeButton.IsEnabled = false;

            try
            {
                var progress = new Progress<int>(value => ProgressBar.Value = value);
                _logEntries = await _logParser.ParseLogFileAsync(FilePathTextBox.Text, progress);

                if (_logEntries.Count == 0)
                {
                    MessageBox.Show("No valid log entries found.");
                    return;
                }

                _filteredEntries = _logEntries;
                await PerformAnalysis();
                EnableExportButtons();
                _alertTimer.Start();
                MessageBox.Show($"Analysis complete. Processed {_logEntries.Count} log entries.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error analyzing log file: {ex.Message}");
            }
            finally
            {
                ProgressBar.Visibility = Visibility.Collapsed;
                AnalyzeButton.IsEnabled = true;
            }
        }

        private async Task PerformAnalysis()
        {
            var entriesToAnalyze = _filteredEntries ?? _logEntries;
            
            await Task.Run(() =>
            {
                // Traffic Analysis
                var trafficByHour = _analysisService.GetTrafficByHour(entriesToAnalyze);
                _analysisResults["Traffic by Hour"] = trafficByHour;

                // Performance Analysis
                var statusCodes = _analysisService.GetStatusCodeDistribution(entriesToAnalyze);
                var slowRequests = _analysisService.GetSlowRequests(entriesToAnalyze);
                var responseTimePercentiles = _analysisService.GetResponseTimePercentiles(entriesToAnalyze);
                _analysisResults["Status Codes"] = statusCodes;
                _analysisResults["Slow Requests"] = slowRequests;
                _analysisResults["Response Time Percentiles"] = responseTimePercentiles;

                // Security Analysis
                var suspiciousActivity = _analysisService.GetSuspiciousActivity(entriesToAnalyze);
                var errors = _analysisService.GetErrorEntries(entriesToAnalyze);
                var botDetection = _analysisService.GetBotDetection(entriesToAnalyze);
                var attackPatterns = _analysisService.GetAttackPatterns(entriesToAnalyze);
                _analysisResults["Suspicious Activity"] = suspiciousActivity;
                _analysisResults["Errors"] = errors;
                _analysisResults["Bot Detection"] = botDetection;
                _analysisResults["Attack Patterns"] = attackPatterns;

                // User Behavior
                var topPages = _analysisService.GetTopPages(entriesToAnalyze);
                var topIPs = _analysisService.GetTopIPs(entriesToAnalyze);
                var sessionAnalysis = _analysisService.GetSessionAnalysis(entriesToAnalyze);
                _analysisResults["Top Pages"] = topPages;
                _analysisResults["Top IPs"] = topIPs;
                _analysisResults["Session Analysis"] = sessionAnalysis;

                // Bandwidth Analysis
                var bandwidthByHour = _analysisService.GetBandwidthByHour(entriesToAnalyze);
                _analysisResults["Bandwidth by Hour"] = bandwidthByHour;
            });

            Dispatcher.Invoke(() =>
            {
                UpdateCharts();
                UpdateGrids();
                UpdateAdvancedAnalysis();
            });
        }

        private void UpdateCharts()
        {
            // Traffic Chart
            if (_analysisResults["Traffic by Hour"] is Dictionary<string, int> trafficData)
            {
                TrafficChart.Series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "Requests per Hour",
                        Values = new ChartValues<int>(trafficData.Values)
                    }
                };
                TrafficChart.AxisX.Add(new Axis
                {
                    Title = "Hour",
                    Labels = trafficData.Keys.ToArray()
                });
            }

            // Status Code Chart
            if (_analysisResults["Status Codes"] is Dictionary<int, int> statusData)
            {
                StatusChart.Series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "Status Codes",
                        Values = new ChartValues<int>(statusData.Values)
                    }
                };
                StatusChart.AxisX.Add(new Axis
                {
                    Title = "Status Code",
                    Labels = statusData.Keys.Select(k => k.ToString()).ToArray()
                });
            }

            // Top Pages Pie Chart
            if (_analysisResults["Top Pages"] is Dictionary<string, int> pagesData)
            {
                TopPagesChart.Series = new SeriesCollection();
                foreach (var item in pagesData.Take(5))
                {
                    TopPagesChart.Series.Add(new PieSeries
                    {
                        Title = item.Key,
                        Values = new ChartValues<int> { item.Value }
                    });
                }
            }

            // Bandwidth Chart
            if (_analysisResults["Bandwidth by Hour"] is Dictionary<string, long> bandwidthData)
            {
                BandwidthChart.Series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "Bandwidth (Bytes)",
                        Values = new ChartValues<long>(bandwidthData.Values)
                    }
                };
                BandwidthChart.AxisX.Add(new Axis
                {
                    Title = "Hour",
                    Labels = bandwidthData.Keys.ToArray()
                });
            }
        }

        private void UpdateGrids()
        {
            if (_analysisResults["Traffic by Hour"] is Dictionary<string, int> trafficData)
            {
                TrafficGrid.ItemsSource = trafficData.Select(kvp => new { Hour = kvp.Key, Requests = kvp.Value });
            }

            if (_analysisResults["Slow Requests"] is List<LogEntry> slowRequests)
            {
                SlowRequestsGrid.ItemsSource = slowRequests.Take(100);
            }

            if (_analysisResults["Suspicious Activity"] is List<LogEntry> suspicious)
            {
                SuspiciousIPsGrid.ItemsSource = suspicious.GroupBy(e => e.ClientIP)
                    .Select(g => new { IP = g.Key, Count = g.Count() });
            }

            if (_analysisResults["Errors"] is List<LogEntry> errors)
            {
                ErrorsGrid.ItemsSource = errors.Take(100);
            }

            if (_analysisResults["Top IPs"] is Dictionary<string, int> topIPs)
            {
                TopIPsGrid.ItemsSource = topIPs.Select(kvp => new { IP = kvp.Key, Requests = kvp.Value });
            }
        }

        private void EnableExportButtons()
        {
            ExportPdfButton.IsEnabled = true;
            ExportExcelButton.IsEnabled = true;
            ExportCsvButton.IsEnabled = true;
        }

        private void ExportPdf_Click(object sender, RoutedEventArgs e)
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf",
                DefaultExt = "pdf"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    _exportService.ExportToPdf(_analysisResults, saveDialog.FileName);
                    MessageBox.Show("PDF exported successfully!");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting PDF: {ex.Message}");
                }
            }
        }

        private void ExportExcel_Click(object sender, RoutedEventArgs e)
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "Excel files (*.xlsx)|*.xlsx",
                DefaultExt = "xlsx"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    _exportService.ExportToExcel(_analysisResults, saveDialog.FileName);
                    MessageBox.Show("Excel file exported successfully!");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting Excel: {ex.Message}");
                }
            }
        }

        private void ExportCsv_Click(object sender, RoutedEventArgs e)
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv",
                DefaultExt = "csv"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    if (_analysisResults["Traffic by Hour"] is Dictionary<string, int> data)
                    {
                        _exportService.ExportToCsv(data, saveDialog.FileName);
                        MessageBox.Show("CSV file exported successfully!");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting CSV: {ex.Message}");
                }
            }
        }
        
        private void UpdateAdvancedAnalysis()
        {
            // Bot Detection
            if (_analysisResults["Bot Detection"] is Dictionary<string, int> botData)
            {
                BotDetectionGrid.ItemsSource = botData.Select(kvp => new { UserAgent = kvp.Key, Count = kvp.Value });
            }
            
            // Attack Patterns
            if (_analysisResults["Attack Patterns"] is Dictionary<string, int> attackData)
            {
                AttackPatternsGrid.ItemsSource = attackData.Select(kvp => new { Attack = kvp.Key, Count = kvp.Value });
            }
            
            // Response Time Percentiles
            if (_analysisResults["Response Time Percentiles"] is Dictionary<string, double> percentileData)
            {
                ResponseTimeChart.Series = new SeriesCollection();
                foreach (var item in percentileData)
                {
                    ResponseTimeChart.Series.Add(new PieSeries
                    {
                        Title = item.Key,
                        Values = new ChartValues<double> { item.Value }
                    });
                }
            }
            
            // Session Analysis
            if (_analysisResults["Session Analysis"] is Dictionary<string, object> sessionData)
            {
                SessionAnalysisGrid.ItemsSource = sessionData.Select(kvp => new { Metric = kvp.Key, Value = kvp.Value });
            }
        }
        
        private void ApplyFilters_Click(object sender, RoutedEventArgs e)
        {
            if (_logEntries == null) return;
            
            var criteria = new FilterCriteria
            {
                StartDate = StartDatePicker.SelectedDate,
                EndDate = EndDatePicker.SelectedDate,
                IPAddress = IPFilterTextBox.Text,
                StatusCode = StatusCodeComboBox.SelectedItem?.ToString() != "All" ? 
                    int.TryParse(StatusCodeComboBox.SelectedItem?.ToString(), out int code) ? code : null : null
            };
            
            _filteredEntries = _analysisService.FilterEntries(_logEntries, criteria);
            
            Task.Run(async () => await PerformAnalysis());
        }
        
        private void ClearFilters_Click(object sender, RoutedEventArgs e)
        {
            StartDatePicker.SelectedDate = null;
            EndDatePicker.SelectedDate = null;
            IPFilterTextBox.Text = "";
            StatusCodeComboBox.SelectedIndex = 0;
            
            _filteredEntries = _logEntries;
            if (_logEntries != null)
            {
                Task.Run(async () => await PerformAnalysis());
            }
        }
        
        private void RealtimeMonitoring_Checked(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(FilePathTextBox.Text))
            {
                _realtimeMonitor.StartMonitoring(FilePathTextBox.Text);
            }
        }
        
        private void RealtimeMonitoring_Unchecked(object sender, RoutedEventArgs e)
        {
            _realtimeMonitor.StopMonitoring();
        }
        
        private void OnNewEntriesDetected(List<LogEntry> newEntries)
        {
            Dispatcher.Invoke(() =>
            {
                _logEntries.AddRange(newEntries);
                if (_filteredEntries != null)
                {
                    _filteredEntries.AddRange(newEntries);
                }
                Task.Run(async () => await PerformAnalysis());
            });
        }
        
        private void AlertTimer_Tick(object sender, EventArgs e)
        {
            if (_logEntries != null)
            {
                var alerts = _alertService.CheckAlerts(_logEntries);
                Dispatcher.Invoke(() =>
                {
                    AlertsGrid.ItemsSource = alerts;
                });
            }
        }
    }
}