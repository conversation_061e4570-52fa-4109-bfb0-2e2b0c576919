using System;
using System.Collections.Generic;
using System.Linq;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    public class AnalysisService
    {
        public Dictionary<string, int> GetTrafficByHour(List<LogEntry> entries)
        {
            return entries.GroupBy(e => e.DateTime.Hour.ToString("D2"))
                         .ToDictionary(g => g.Key, g => g.Count());
        }

        public Dictionary<int, int> GetStatusCodeDistribution(List<LogEntry> entries)
        {
            return entries.GroupBy(e => e.StatusCode)
                         .ToDictionary(g => g.Key, g => g.Count());
        }

        public List<LogEntry> GetSlowRequests(List<LogEntry> entries, int threshold = 5000)
        {
            return entries.Where(e => e.TimeTaken > threshold).ToList();
        }

        public Dictionary<string, int> GetTopPages(List<LogEntry> entries, int count = 10)
        {
            return entries.GroupBy(e => e.UriStem)
                         .OrderByDescending(g => g.Count())
                         .Take(count)
                         .ToDictionary(g => g.Key, g => g.Count());
        }

        public Dictionary<string, int> GetTopIPs(List<LogEntry> entries, int count = 10)
        {
            return entries.GroupBy(e => e.ClientIP)
                         .OrderByDescending(g => g.Count())
                         .Take(count)
                         .ToDictionary(g => g.Key, g => g.Count());
        }

        public List<LogEntry> GetErrorEntries(List<LogEntry> entries)
        {
            return entries.Where(e => e.StatusCode >= 400).ToList();
        }

        public Dictionary<string, long> GetBandwidthByHour(List<LogEntry> entries)
        {
            return entries.GroupBy(e => e.DateTime.Hour.ToString("D2"))
                         .ToDictionary(g => g.Key, g => g.Sum(e => e.BytesSent));
        }

        public List<LogEntry> GetSuspiciousActivity(List<LogEntry> entries)
        {
            var suspiciousIPs = entries.GroupBy(e => e.ClientIP)
                                     .Where(g => g.Count() > 1000 || g.Count(x => x.StatusCode == 404) > 50)
                                     .Select(g => g.Key);

            return entries.Where(e => suspiciousIPs.Contains(e.ClientIP)).ToList();
        }

        public List<LogEntry> FilterEntries(List<LogEntry> entries, FilterCriteria criteria)
        {
            var filtered = entries.AsEnumerable();
            
            if (criteria.StartDate.HasValue)
                filtered = filtered.Where(e => e.DateTime >= criteria.StartDate.Value);
            if (criteria.EndDate.HasValue)
                filtered = filtered.Where(e => e.DateTime <= criteria.EndDate.Value);
            if (!string.IsNullOrEmpty(criteria.IPAddress))
                filtered = filtered.Where(e => e.ClientIP.Contains(criteria.IPAddress));
            if (criteria.StatusCode.HasValue)
                filtered = filtered.Where(e => e.StatusCode == criteria.StatusCode.Value);
            if (!string.IsNullOrEmpty(criteria.Method))
                filtered = filtered.Where(e => e.Method == criteria.Method);
            if (!string.IsNullOrEmpty(criteria.UriPattern))
                filtered = filtered.Where(e => e.UriStem.Contains(criteria.UriPattern));
            if (criteria.MinResponseTime.HasValue)
                filtered = filtered.Where(e => e.TimeTaken >= criteria.MinResponseTime.Value);
            if (criteria.MaxResponseTime.HasValue)
                filtered = filtered.Where(e => e.TimeTaken <= criteria.MaxResponseTime.Value);
                
            return filtered.ToList();
        }

        public Dictionary<string, int> GetBotDetection(List<LogEntry> entries)
        {
            var botPatterns = new[] { "bot", "crawler", "spider", "scraper", "curl", "wget" };
            return entries.Where(e => botPatterns.Any(p => e.UserAgent.ToLower().Contains(p)))
                         .GroupBy(e => e.UserAgent)
                         .ToDictionary(g => g.Key, g => g.Count());
        }

        public Dictionary<string, double> GetResponseTimePercentiles(List<LogEntry> entries)
        {
            var times = entries.Select(e => (double)e.TimeTaken).OrderBy(t => t).ToList();
            if (!times.Any()) return new Dictionary<string, double>();
            
            return new Dictionary<string, double>
            {
                ["P50"] = GetPercentile(times, 0.5),
                ["P90"] = GetPercentile(times, 0.9),
                ["P95"] = GetPercentile(times, 0.95),
                ["P99"] = GetPercentile(times, 0.99)
            };
        }

        private double GetPercentile(List<double> values, double percentile)
        {
            int index = (int)Math.Ceiling(values.Count * percentile) - 1;
            return values[Math.Max(0, Math.Min(index, values.Count - 1))];
        }

        public Dictionary<string, int> GetAttackPatterns(List<LogEntry> entries)
        {
            var attacks = new Dictionary<string, string[]>
            {
                ["SQL Injection"] = new[] { "union", "select", "insert", "drop", "'", "--" },
                ["XSS"] = new[] { "<script", "javascript:", "onerror", "onload" },
                ["Path Traversal"] = new[] { "../", "..\\" },
                ["Command Injection"] = new[] { ";", "|", "&", "`" }
            };
            
            var results = new Dictionary<string, int>();
            foreach (var attack in attacks)
            {
                results[attack.Key] = entries.Count(e => 
                    attack.Value.Any(pattern => 
                        e.UriStem.ToLower().Contains(pattern) || 
                        e.UriQuery.ToLower().Contains(pattern)));
            }
            return results;
        }

        public Dictionary<string, object> GetSessionAnalysis(List<LogEntry> entries)
        {
            var sessions = entries.GroupBy(e => e.ClientIP)
                                 .Select(g => new {
                                     IP = g.Key,
                                     Duration = g.Max(x => x.DateTime) - g.Min(x => x.DateTime),
                                     PageViews = g.Count(),
                                     UniquePages = g.Select(x => x.UriStem).Distinct().Count()
                                 }).ToList();
            
            return new Dictionary<string, object>
            {
                ["Average Session Duration"] = sessions.Average(s => s.Duration.TotalMinutes),
                ["Average Page Views"] = sessions.Average(s => s.PageViews),
                ["Bounce Rate"] = sessions.Count(s => s.PageViews == 1) * 100.0 / sessions.Count
            };
        }
    }
}