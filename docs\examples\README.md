# AADHAAR Authentication API Code Examples

## Overview
This directory contains comprehensive code examples and sample implementations for the AADHAAR Authentication API 2.5. Examples are provided in multiple programming languages and cover all authentication methods.

## Directory Structure
```
examples/
├── README.md                    # This file
├── basic-auth/                  # Basic authentication examples
│   ├── demographic-auth.js      # Demographic authentication
│   ├── biometric-auth.js        # Biometric authentication
│   ├── otp-auth.js             # OTP authentication
│   └── ekyc-auth.js            # eKYC authentication
├── security/                    # Security implementation examples
│   ├── encryption.js           # Encryption/decryption utilities
│   ├── digital-signatures.js   # Digital signature implementation
│   ├── certificate-manager.js  # Certificate management
│   └── session-key-manager.js  # Session key handling
├── request-response/            # Sample requests and responses
│   ├── auth-request.xml        # Sample authentication request
│   ├── auth-response.xml       # Sample authentication response
│   ├── otp-request.xml         # Sample OTP request
│   ├── ekyc-request.xml        # Sample eKYC request
│   └── error-responses.xml     # Sample error responses
├── integration-patterns/        # Common integration patterns
│   ├── express-middleware.js   # Express.js middleware
│   ├── spring-boot-service.java # Spring Boot service
│   ├── python-client.py        # Python client implementation
│   └── php-integration.php     # PHP integration example
├── testing/                     # Testing examples
│   ├── unit-tests.js           # Unit test examples
│   ├── integration-tests.js    # Integration test examples
│   └── mock-responses.js       # Mock response utilities
└── utilities/                   # Helper utilities
    ├── xml-builder.js          # XML request builder
    ├── validator.js            # Input validation
    ├── logger.js               # Audit logging
    └── config-manager.js       # Configuration management
```

## Quick Start Examples

### 1. Basic Demographic Authentication
```javascript
// Basic demographic authentication example
const aadhaarAuth = new AadhaarAuthService(config);

const authRequest = {
  uid: '123456789012',
  authType: 'demographic',
  pid: {
    demo: {
      pi: {
        name: 'John Doe',
        dob: '1990-01-15',
        gender: 'M'
      },
      pa: {
        co: 'IN',
        state: 'Karnataka',
        dist: 'Bangalore Urban',
        pc: '560001'
      }
    }
  }
};

const result = await aadhaarAuth.authenticate(authRequest);
console.log('Authentication result:', result);
```

### 2. OTP Authentication Flow
```javascript
// Step 1: Generate OTP
const otpResponse = await aadhaarAuth.generateOTP({
  uid: '123456789012',
  channel: '01' // SMS
});

// Step 2: Authenticate with OTP
const authResult = await aadhaarAuth.authenticateOTP({
  uid: '123456789012',
  otp: '123456',
  txnId: otpResponse.txnId
});
```

### 3. eKYC Data Retrieval
```javascript
// eKYC authentication with data retrieval
const ekycResult = await aadhaarAuth.performEKYC({
  uid: '123456789012',
  authType: 'biometric',
  biometric: {
    type: 'FMR',
    data: 'base64_encoded_biometric_data'
  },
  consent: true
});

console.log('eKYC Data:', ekycResult.kycData);
```

## Language-Specific Examples

### JavaScript/Node.js
- **Framework**: Express.js with crypto library
- **Features**: Async/await, modern ES6+ syntax
- **Security**: Built-in crypto module for encryption

### Java
- **Framework**: Spring Boot
- **Features**: Annotation-based configuration
- **Security**: Bouncy Castle for cryptographic operations

### Python
- **Framework**: Flask/FastAPI
- **Features**: Type hints and async support
- **Security**: cryptography library

### PHP
- **Framework**: Laravel/Symfony compatible
- **Features**: PSR-4 autoloading
- **Security**: OpenSSL extension

## Security Implementation Examples

### Encryption Service
```javascript
class EncryptionService {
  constructor(config) {
    this.algorithm = 'aes-256-gcm';
    this.keySize = 32; // 256 bits
  }
  
  generateSessionKey() {
    return crypto.randomBytes(this.keySize);
  }
  
  encryptPID(pidXml, sessionKey) {
    const cipher = crypto.createCipher(this.algorithm, sessionKey);
    let encrypted = cipher.update(pidXml, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  }
  
  encryptSessionKey(sessionKey, uidaiPublicKey) {
    return crypto.publicEncrypt({
      key: uidaiPublicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
    }, sessionKey);
  }
}
```

### Digital Signature Service
```javascript
class DigitalSignatureService {
  constructor(privateKey, certificate) {
    this.privateKey = privateKey;
    this.certificate = certificate;
  }
  
  signXML(xmlData) {
    const canonicalXml = this.canonicalizeXML(xmlData);
    const hash = crypto.createHash('sha256').update(canonicalXml).digest();
    const signature = crypto.sign('RSA-SHA256', hash, this.privateKey);
    return signature.toString('base64');
  }
  
  verifySignature(xmlData, signature, publicKey) {
    const canonicalXml = this.canonicalizeXML(xmlData);
    const hash = crypto.createHash('sha256').update(canonicalXml).digest();
    return crypto.verify('RSA-SHA256', hash, publicKey, 
                        Buffer.from(signature, 'base64'));
  }
}
```

## Integration Patterns

### Express.js Middleware
```javascript
// Aadhaar authentication middleware for Express.js
function aadhaarAuthMiddleware(options = {}) {
  const authService = new AadhaarAuthService(options.config);
  
  return async (req, res, next) => {
    try {
      const authData = req.body;
      const result = await authService.authenticate(authData);
      
      if (result.ret === 'y') {
        req.aadhaarAuth = result;
        next();
      } else {
        res.status(401).json({
          error: 'Authentication failed',
          code: result.code,
          message: result.err
        });
      }
    } catch (error) {
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  };
}
```

### Spring Boot Service
```java
@Service
public class AadhaarAuthService {
    
    @Autowired
    private EncryptionService encryptionService;
    
    @Autowired
    private CertificateManager certificateManager;
    
    public AuthResponse authenticate(AuthRequest request) {
        try {
            // Validate request
            validateAuthRequest(request);
            
            // Generate session key
            byte[] sessionKey = generateSessionKey();
            
            // Encrypt PID
            String encryptedPID = encryptionService.encryptPID(
                request.getPidData(), sessionKey);
            
            // Build and send request
            String xmlRequest = buildAuthXML(request, encryptedPID, sessionKey);
            String response = sendToUidai(xmlRequest);
            
            return parseAuthResponse(response);
            
        } catch (Exception e) {
            log.error("Authentication failed", e);
            throw new AuthenticationException("Authentication failed", e);
        }
    }
}
```

## Testing Examples

### Unit Tests
```javascript
describe('Aadhaar Authentication Service', () => {
  let authService;
  let mockConfig;
  
  beforeEach(() => {
    mockConfig = {
      certificates: {
        auaCert: 'mock-aua-cert',
        uidaiPublicKey: 'mock-uidai-key'
      }
    };
    authService = new AadhaarAuthService(mockConfig);
  });
  
  test('should generate valid session key', () => {
    const sessionKey = authService.generateSessionKey();
    expect(sessionKey).toHaveLength(32);
    expect(Buffer.isBuffer(sessionKey)).toBe(true);
  });
  
  test('should encrypt PID data correctly', () => {
    const pidData = '<Pid>test data</Pid>';
    const sessionKey = Buffer.from('test-session-key-32-bytes-long!!');
    
    const encrypted = authService.encryptPID(pidData, sessionKey);
    expect(encrypted).toBeDefined();
    expect(typeof encrypted).toBe('string');
  });
});
```

### Integration Tests
```javascript
describe('Aadhaar API Integration', () => {
  test('demographic authentication flow', async () => {
    const authRequest = {
      uid: '************', // Test UID
      authType: 'demographic',
      pid: {
        demo: {
          pi: { name: 'Test User', dob: '1990-01-01', gender: 'M' }
        }
      }
    };
    
    const result = await authService.authenticate(authRequest);
    
    expect(result.ret).toBe('y');
    expect(result.code).toBe('0000');
    expect(result.txn).toBeDefined();
  });
});
```

## Configuration Examples

### Development Configuration
```json
{
  "environment": "development",
  "api": {
    "baseUrl": "https://developer.uidai.gov.in/auth",
    "version": "2.5",
    "timeout": 30000
  },
  "certificates": {
    "auaCertPath": "./certs/aua-dev.pem",
    "auaPrivateKeyPath": "./certs/aua-dev-private.key",
    "uidaiPublicKeyPath": "./certs/uidai-public.pem"
  },
  "security": {
    "encryptionAlgorithm": "aes-256-gcm",
    "signatureAlgorithm": "RSA-SHA256"
  }
}
```

### Production Configuration
```json
{
  "environment": "production",
  "api": {
    "baseUrl": "https://auth.uidai.gov.in/auth",
    "version": "2.5",
    "timeout": 30000,
    "retryAttempts": 3
  },
  "security": {
    "hsmEnabled": true,
    "certificateValidation": "strict",
    "auditLogging": true
  },
  "monitoring": {
    "healthChecks": true,
    "performanceMetrics": true,
    "errorTracking": true
  }
}
```

---

**Usage**: Browse the specific example files in each subdirectory for detailed implementation code and explanations.
