# AADHAAR Authentication API Error Handling Guide

## Table of Contents
- [Error Response Structure](#error-response-structure)
- [Authentication Error Codes](#authentication-error-codes)
- [Technical Error Codes](#technical-error-codes)
- [Business Logic Errors](#business-logic-errors)
- [HTTP Status Codes](#http-status-codes)
- [Error Handling Best Practices](#error-handling-best-practices)
- [Troubleshooting Guide](#troubleshooting-guide)

## Error Response Structure

### Standard Error Response Format
```xml
<?xml version="1.0" encoding="UTF-8"?>
<AuthRes ret="n" code="ERROR_CODE" txn="TRANSACTION_ID" 
         ts="TIMESTAMP" err="ERROR_MESSAGE" info="ADDITIONAL_INFO"/>
```

### Error Response Fields
| Field | Type | Description |
|-------|------|-------------|
| `ret` | String | Return status: "y" (success) or "n" (failure) |
| `code` | String | 4-digit error code |
| `txn` | String | Transaction ID from request |
| `ts` | DateTime | Response timestamp |
| `err` | String | Human-readable error message |
| `info` | String | Additional error information |

## Authentication Error Codes

### 1. Demographic Authentication Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `300` | Invalid UID | Aadhaar number is invalid | Verify 12-digit Aadhaar number format |
| `310` | UID not found | Aadhaar number not in database | Check Aadhaar number validity |
| `320` | Authentication failed | Demographic data mismatch | Verify name, DOB, gender, address |
| `330` | Partial match | Some demographic fields matched | Review and correct mismatched fields |
| `340` | Locked UID | Aadhaar number is locked | Contact UIDAI for unlock procedure |

### 2. Biometric Authentication Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `400` | Biometric mismatch | Biometric data doesn't match | Retry with better quality biometric |
| `410` | Poor biometric quality | Biometric quality insufficient | Improve capture conditions |
| `420` | Biometric not available | No biometric data in Aadhaar | Use alternative authentication method |
| `430` | Multiple biometric match | Ambiguous biometric match | Use additional authentication factors |
| `440` | Biometric device error | Device-related biometric error | Check device certification and drivers |

### 3. OTP Authentication Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `500` | Invalid OTP | OTP is incorrect | Enter correct OTP |
| `510` | OTP expired | OTP validity period expired | Generate new OTP |
| `520` | OTP not generated | OTP generation failed | Retry OTP generation |
| `530` | OTP limit exceeded | Too many OTP attempts | Wait for cooldown period |
| `540` | Mobile not registered | Mobile number not linked | Update mobile number with UIDAI |

### 4. eKYC Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `600` | eKYC not allowed | eKYC service not permitted | Check AUA permissions |
| `610` | eKYC data unavailable | Demographic data not available | Use authentication-only service |
| `620` | eKYC consent required | User consent not provided | Obtain explicit user consent |
| `630` | eKYC limit exceeded | Daily eKYC limit reached | Wait for limit reset |

## Technical Error Codes

### 1. Request Format Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `700` | Invalid XML format | Malformed XML request | Validate XML structure |
| `710` | Missing mandatory field | Required field not provided | Include all mandatory fields |
| `720` | Invalid field value | Field contains invalid data | Validate field formats and values |
| `730` | Invalid timestamp | Timestamp format incorrect | Use ISO 8601 format |
| `740` | Request too old | Request timestamp too old | Send request within time window |

### 2. Security Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `800` | Invalid signature | Digital signature verification failed | Check certificate and signing process |
| `810` | Certificate expired | AUA certificate has expired | Renew certificate |
| `820` | Certificate revoked | Certificate has been revoked | Obtain new certificate |
| `830` | Encryption error | Data encryption/decryption failed | Verify encryption implementation |
| `840` | HMAC verification failed | Message integrity check failed | Verify HMAC calculation |

### 3. System Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `900` | System unavailable | UIDAI system temporarily down | Retry after some time |
| `910` | Database error | Internal database error | Contact UIDAI support |
| `920` | Network timeout | Request processing timeout | Increase timeout or retry |
| `930` | Rate limit exceeded | Too many requests | Implement rate limiting |
| `940` | Maintenance mode | System under maintenance | Check maintenance schedule |

## Business Logic Errors

### 1. Authorization Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `100` | AUA not registered | AUA code not found | Complete AUA registration |
| `110` | Sub-AUA not registered | Sub-AUA code not found | Register Sub-AUA |
| `120` | Service not allowed | Authentication type not permitted | Check service permissions |
| `130` | Transaction limit exceeded | Daily/monthly limit reached | Wait for limit reset |
| `140` | Invalid license | AUA license invalid or expired | Renew AUA license |

### 2. Data Validation Errors
| Code | Error Message | Description | Resolution |
|------|---------------|-------------|------------|
| `200` | Invalid transaction ID | Transaction ID format incorrect | Use proper TID format |
| `210` | Duplicate transaction | Transaction ID already used | Use unique transaction ID |
| `220` | Invalid AUA code | AUA code format incorrect | Verify AUA code format |
| `230` | Invalid device info | Device information invalid | Update device metadata |
| `240` | Consent not provided | User consent missing | Obtain user consent |

## HTTP Status Codes

### Standard HTTP Responses
| Status Code | Meaning | When Used |
|-------------|---------|-----------|
| `200 OK` | Success | Valid request processed (check XML for auth result) |
| `400 Bad Request` | Client Error | Malformed request, invalid XML |
| `401 Unauthorized` | Authentication Failed | Invalid certificates or credentials |
| `403 Forbidden` | Access Denied | AUA not authorized for service |
| `429 Too Many Requests` | Rate Limited | Request rate exceeded |
| `500 Internal Server Error` | Server Error | UIDAI system error |
| `503 Service Unavailable` | Service Down | System maintenance or overload |

## Error Handling Best Practices

### 1. Error Handling Implementation
```javascript
class ErrorHandler {
  handleAuthResponse(response) {
    if (response.ret === 'n') {
      const error = this.parseError(response);
      
      switch (error.category) {
        case 'AUTHENTICATION':
          return this.handleAuthError(error);
        case 'TECHNICAL':
          return this.handleTechnicalError(error);
        case 'BUSINESS':
          return this.handleBusinessError(error);
        default:
          return this.handleUnknownError(error);
      }
    }
    
    return this.handleSuccess(response);
  }
  
  parseError(response) {
    return {
      code: response.code,
      message: response.err,
      info: response.info,
      category: this.categorizeError(response.code),
      retryable: this.isRetryable(response.code),
      userAction: this.getUserAction(response.code)
    };
  }
}
```

### 2. Retry Logic
```javascript
class RetryHandler {
  async executeWithRetry(operation, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        
        if (result.ret === 'y') {
          return result;
        }
        
        if (!this.isRetryable(result.code)) {
          throw new Error(`Non-retryable error: ${result.err}`);
        }
        
        if (attempt < maxRetries) {
          await this.delay(this.getRetryDelay(attempt));
        }
        
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        await this.delay(this.getRetryDelay(attempt));
      }
    }
  }
  
  isRetryable(errorCode) {
    const retryableCodes = ['900', '910', '920', '930'];
    return retryableCodes.includes(errorCode);
  }
  
  getRetryDelay(attempt) {
    return Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff
  }
}
```

### 3. User-Friendly Error Messages
```javascript
const ERROR_MESSAGES = {
  '300': 'Please check your Aadhaar number and try again.',
  '320': 'The information provided does not match our records. Please verify and try again.',
  '400': 'Biometric verification failed. Please try again with a clear scan.',
  '500': 'Invalid OTP. Please enter the correct OTP.',
  '510': 'OTP has expired. Please request a new OTP.',
  '800': 'Security verification failed. Please contact support.',
  '900': 'Service temporarily unavailable. Please try again later.'
};

function getUserFriendlyMessage(errorCode) {
  return ERROR_MESSAGES[errorCode] || 'An error occurred. Please try again or contact support.';
}
```

## Troubleshooting Guide

### 1. Common Issues and Solutions

#### Issue: Certificate Errors (8xx codes)
**Symptoms**: Authentication fails with certificate-related errors
**Solutions**:
1. Verify certificate validity and expiration
2. Check certificate chain and root CA
3. Ensure proper certificate format (PEM)
4. Validate private key matches certificate

#### Issue: Encryption Errors (83x codes)
**Symptoms**: Data encryption/decryption failures
**Solutions**:
1. Verify session key generation
2. Check UIDAI public key version
3. Validate encryption algorithm implementation
4. Ensure proper padding and encoding

#### Issue: Rate Limiting (930 code)
**Symptoms**: Requests rejected due to rate limits
**Solutions**:
1. Implement exponential backoff
2. Distribute requests across time
3. Monitor request patterns
4. Contact UIDAI for limit increase

### 2. Diagnostic Tools
```javascript
class DiagnosticTools {
  async runDiagnostics() {
    const results = {
      certificateStatus: await this.checkCertificates(),
      networkConnectivity: await this.checkConnectivity(),
      encryptionTest: await this.testEncryption(),
      xmlValidation: await this.validateXMLGeneration(),
      systemHealth: await this.checkSystemHealth()
    };
    
    return this.generateDiagnosticReport(results);
  }
  
  generateDiagnosticReport(results) {
    return {
      timestamp: new Date().toISOString(),
      overallStatus: this.calculateOverallStatus(results),
      details: results,
      recommendations: this.generateRecommendations(results)
    };
  }
}
```

### 3. Monitoring and Alerting
```yaml
# Error Monitoring Configuration
monitoring:
  error_thresholds:
    authentication_failures: 
      threshold: 10
      window: "5 minutes"
      action: "alert"
    
    technical_errors:
      threshold: 5
      window: "1 minute"
      action: "escalate"
    
    certificate_expiry:
      threshold: "30 days"
      action: "notify"
  
  alerting:
    channels: ["email", "slack", "sms"]
    escalation_levels: ["warning", "critical", "emergency"]
```

---

**Next**: Implement comprehensive error handling using these templates and guidelines to ensure robust authentication service operation.
