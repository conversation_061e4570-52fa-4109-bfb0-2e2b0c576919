<?xml version="1.0" encoding="UTF-8"?>
<!-- 
  Sample AADHAAR Authentication Request XML
  
  This is a complete example of an authentication request for demographic verification.
  Replace placeholder values with actual data before use.
-->

<Auth uid="123456789012" 
      tid="AUA:TXN20240115103000123" 
      ac="AUA_CODE_HERE" 
      sa="SUB_AUA_CODE_HERE" 
      ver="2.5" 
      txn="TXN20240115103000123" 
      ts="2024-01-15T10:30:00">

  <!-- 
    Uses element specifies which authentication factors to use:
    - pi: Personal Identity (name, DOB, gender)
    - pa: Personal Address
    - pfa: Personal Full Address
    - bio: Biometric data
    - bt: Biometric type (FMR=Fingerprint Minutiae, FIR=Fingerprint Image, IIR=Iris Image)
    - otp: One Time Password
    - ekyc: Electronic Know Your Customer
  -->
  <Uses pi="y" pa="y" pfa="n" bio="n" bt="" otp="n" ekyc="n"/>

  <!-- 
    Meta element contains metadata about the request:
    - udc: User device code
    - fdc: Finger device code
    - idc: Iris device code
    - pip: Public IP address of the device
    - lot: Location type (P=Pincode, G=GPS coordinates)
    - lov: Location value (pincode or GPS coordinates)
  -->
  <Meta udc="public" 
        fdc="NC" 
        idc="NA" 
        pip="*************" 
        lot="P" 
        lov="560001"/>

  <!-- 
    Skey element contains the encrypted session key:
    - ci: Certificate identifier used for encryption
    - Content: Base64 encoded session key encrypted with UIDAI public key
  -->
  <Skey ci="20240101">MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...</Skey>

  <!-- 
    Data element contains the encrypted PID (Personal Identity Data) block:
    - type: Always "X" for encrypted data
    - Content: Base64 encoded PID XML encrypted with session key
  -->
  <Data type="X">eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ...</Data>

  <!-- 
    Hmac element contains the HMAC-SHA256 of the entire Auth element:
    - Calculated using the session key
    - Ensures message integrity
  -->
  <Hmac>a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456</Hmac>

</Auth>

<!-- 
  Sample PID (Personal Identity Data) block structure (before encryption):
  
  <?xml version="1.0" encoding="UTF-8"?>
  <Pid ts="2024-01-15T10:30:00" ver="2.0">
    <Demo lang="en">
      <Pi ms="E" mv="exact" name="John Doe" lname="" dob="1990-01-15" dobType="V" gender="M" phone="9876543210" email="<EMAIL>"/>
      <Pa ms="E" mv="exact" co="IN" country="India" state="Karnataka" dist="Bangalore Urban" subdist="Bangalore South" vtc="Bangalore" po="Koramangala" street="5th Block" house="123" lm="Near Park" loc="Koramangala" pc="560034"/>
      <Pfa ms="E" mv="exact" av="123, 5th Block, Koramangala, Near Park, Bangalore, Karnataka, India - 560034"/>
    </Demo>
  </Pid>

  PID Element Attributes:
  - ts: Timestamp when PID was created
  - ver: PID version (always "2.0")
  
  Demo Element Attributes:
  - lang: Language code (en, hi, etc.)
  
  Pi (Personal Identity) Attributes:
  - ms: Match strategy (E=Exact, P=Partial, F=Fuzzy)
  - mv: Match value (exact, partial, fuzzy)
  - name: Full name as per Aadhaar
  - lname: Local language name
  - dob: Date of birth (YYYY-MM-DD)
  - dobType: DOB type (V=Verified, D=Declared, A=Approximate)
  - gender: Gender (M=Male, F=Female, T=Transgender)
  - phone: Mobile number
  - email: Email address
  
  Pa (Personal Address) Attributes:
  - ms: Match strategy
  - mv: Match value
  - co: Country code (IN for India)
  - country: Country name
  - state: State name
  - dist: District name
  - subdist: Sub-district name
  - vtc: Village/Town/City name
  - po: Post office name
  - street: Street name
  - house: House number
  - lm: Landmark
  - loc: Locality
  - pc: Pincode
  
  Pfa (Personal Full Address) Attributes:
  - ms: Match strategy
  - mv: Match value
  - av: Complete address as single string
-->

<!-- 
  Sample Biometric PID block (for biometric authentication):
  
  <Pid ts="2024-01-15T10:30:00" ver="2.0">
    <Bios>
      <Bio type="FMR" posh="UNKNOWN" bs="base64_encoded_biometric_data_here"/>
    </Bios>
  </Pid>
  
  Bio Element Attributes:
  - type: Biometric type (FMR=Fingerprint Minutiae, FIR=Fingerprint Image, IIR=Iris Image, FACE=Face)
  - posh: Position (for fingerprints: LT=Left Thumb, LI=Left Index, etc.)
  - bs: Base64 encoded biometric data
-->

<!-- 
  Sample OTP PID block (for OTP authentication):
  
  <Pid ts="2024-01-15T10:30:00" ver="2.0">
    <Pv otp="123456" pin=""/>
  </Pid>
  
  Pv Element Attributes:
  - otp: One Time Password received via SMS/Email
  - pin: PIN (if applicable)
-->
