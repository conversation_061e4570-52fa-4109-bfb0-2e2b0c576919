/**
 * AADHAAR Demographic Authentication Example
 * 
 * This example demonstrates how to perform demographic authentication
 * using name, date of birth, gender, and address information.
 */

const crypto = require('crypto');
const fs = require('fs');
const axios = require('axios');

class DemographicAuthService {
  constructor(config) {
    this.config = config;
    this.auaCert = fs.readFileSync(config.certificates.auaCertPath);
    this.auaPrivateKey = fs.readFileSync(config.certificates.auaPrivateKeyPath);
    this.uidaiPublicKey = fs.readFileSync(config.certificates.uidaiPublicKeyPath);
  }

  /**
   * Perform demographic authentication
   * @param {Object} authRequest - Authentication request data
   * @returns {Promise<Object>} Authentication response
   */
  async authenticateDemographic(authRequest) {
    try {
      // 1. Validate input
      this.validateDemographicRequest(authRequest);

      // 2. Generate session key
      const sessionKey = this.generateSessionKey();

      // 3. Build PID block
      const pidXml = this.buildPIDBlock(authRequest.pid);

      // 4. Encrypt PID data
      const encryptedPID = this.encryptPID(pidXml, sessionKey);

      // 5. Encrypt session key
      const encryptedSessionKey = this.encryptSessionKey(sessionKey);

      // 6. Build authentication XML
      const authXml = this.buildAuthXML({
        ...authRequest,
        encryptedPID,
        encryptedSessionKey
      });

      // 7. Calculate HMAC
      const hmac = this.calculateHMAC(authXml, sessionKey);

      // 8. Add HMAC to XML
      const finalXml = this.addHMACToXML(authXml, hmac);

      // 9. Sign the request
      const signedXml = this.signRequest(finalXml);

      // 10. Send to UIDAI
      const response = await this.sendToUidai(signedXml);

      // 11. Parse and return response
      return this.parseAuthResponse(response);

    } catch (error) {
      console.error('Demographic authentication failed:', error);
      throw error;
    }
  }

  /**
   * Validate demographic authentication request
   */
  validateDemographicRequest(request) {
    if (!request.uid || !/^\d{12}$/.test(request.uid)) {
      throw new Error('Invalid UID: Must be 12 digits');
    }

    if (!request.pid || !request.pid.demo) {
      throw new Error('Demographic data is required');
    }

    const demo = request.pid.demo;
    
    // Validate personal identity
    if (demo.pi) {
      if (!demo.pi.name || demo.pi.name.trim().length === 0) {
        throw new Error('Name is required for demographic authentication');
      }
      
      if (demo.pi.dob && !/^\d{4}-\d{2}-\d{2}$/.test(demo.pi.dob)) {
        throw new Error('Invalid date of birth format. Use YYYY-MM-DD');
      }
      
      if (demo.pi.gender && !['M', 'F', 'T'].includes(demo.pi.gender)) {
        throw new Error('Invalid gender. Use M, F, or T');
      }
    }

    // Validate address
    if (demo.pa) {
      if (!demo.pa.co) {
        throw new Error('Country code is required in address');
      }
    }
  }

  /**
   * Generate cryptographically secure session key
   */
  generateSessionKey() {
    return crypto.randomBytes(32); // 256-bit key
  }

  /**
   * Build PID (Personal Identity Data) XML block
   */
  buildPIDBlock(pidData) {
    const timestamp = new Date().toISOString();
    
    let pidXml = `<?xml version="1.0" encoding="UTF-8"?>
<Pid ts="${timestamp}" ver="2.0">`;

    if (pidData.demo) {
      pidXml += '<Demo>';
      
      // Personal Identity
      if (pidData.demo.pi) {
        const pi = pidData.demo.pi;
        pidXml += `<Pi`;
        if (pi.name) pidXml += ` name="${this.escapeXml(pi.name)}"`;
        if (pi.lname) pidXml += ` lname="${this.escapeXml(pi.lname)}"`;
        if (pi.dob) pidXml += ` dob="${pi.dob}"`;
        if (pi.dobType) pidXml += ` dobType="${pi.dobType}"`;
        if (pi.gender) pidXml += ` gender="${pi.gender}"`;
        if (pi.phone) pidXml += ` phone="${pi.phone}"`;
        if (pi.email) pidXml += ` email="${pi.email}"`;
        pidXml += '></Pi>';
      }

      // Personal Address
      if (pidData.demo.pa) {
        const pa = pidData.demo.pa;
        pidXml += `<Pa`;
        if (pa.co) pidXml += ` co="${pa.co}"`;
        if (pa.country) pidXml += ` country="${this.escapeXml(pa.country)}"`;
        if (pa.state) pidXml += ` state="${this.escapeXml(pa.state)}"`;
        if (pa.dist) pidXml += ` dist="${this.escapeXml(pa.dist)}"`;
        if (pa.subdist) pidXml += ` subdist="${this.escapeXml(pa.subdist)}"`;
        if (pa.vtc) pidXml += ` vtc="${this.escapeXml(pa.vtc)}"`;
        if (pa.po) pidXml += ` po="${this.escapeXml(pa.po)}"`;
        if (pa.street) pidXml += ` street="${this.escapeXml(pa.street)}"`;
        if (pa.house) pidXml += ` house="${this.escapeXml(pa.house)}"`;
        if (pa.lm) pidXml += ` lm="${this.escapeXml(pa.lm)}"`;
        if (pa.loc) pidXml += ` loc="${this.escapeXml(pa.loc)}"`;
        if (pa.pc) pidXml += ` pc="${pa.pc}"`;
        pidXml += '></Pa>';
      }

      // Personal Full Address
      if (pidData.demo.pfa) {
        pidXml += `<Pfa av="${this.escapeXml(pidData.demo.pfa.av)}"></Pfa>`;
      }

      pidXml += '</Demo>';
    }

    pidXml += '</Pid>';
    return pidXml;
  }

  /**
   * Encrypt PID data using AES-256-GCM
   */
  encryptPID(pidXml, sessionKey) {
    const cipher = crypto.createCipher('aes-256-gcm', sessionKey);
    let encrypted = cipher.update(pidXml, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  }

  /**
   * Encrypt session key using UIDAI public key
   */
  encryptSessionKey(sessionKey) {
    return crypto.publicEncrypt({
      key: this.uidaiPublicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256'
    }, sessionKey).toString('base64');
  }

  /**
   * Build authentication XML request
   */
  buildAuthXML(params) {
    const timestamp = new Date().toISOString();
    const txnId = params.txnId || this.generateTransactionId();

    return `<?xml version="1.0" encoding="UTF-8"?>
<Auth uid="${params.uid}" 
      tid="${params.tid || 'AUA:' + txnId}" 
      ac="${params.ac || this.config.auaCode}" 
      sa="${params.sa || this.config.subAuaCode}" 
      ver="2.5" 
      txn="${txnId}" 
      ts="${timestamp}">
  <Uses pi="y" pa="y" pfa="n" bio="n" otp="n"/>
  <Meta udc="${params.meta?.udc || 'public'}" 
        fdc="${params.meta?.fdc || 'NC'}" 
        idc="${params.meta?.idc || 'NA'}" 
        pip="${params.meta?.pip || '127.0.0.1'}" 
        lot="${params.meta?.lot || 'P'}" 
        lov="${params.meta?.lov || '560001'}"/>
  <Skey ci="${this.getCertificateIdentifier()}">${params.encryptedSessionKey}</Skey>
  <Data type="X">${params.encryptedPID}</Data>
</Auth>`;
  }

  /**
   * Calculate HMAC for request integrity
   */
  calculateHMAC(xmlData, sessionKey) {
    const hmac = crypto.createHmac('sha256', sessionKey);
    hmac.update(xmlData);
    return hmac.digest('base64');
  }

  /**
   * Add HMAC to XML request
   */
  addHMACToXML(xmlData, hmac) {
    return xmlData.replace('</Auth>', `  <Hmac>${hmac}</Hmac>\n</Auth>`);
  }

  /**
   * Sign the XML request with digital signature
   */
  signRequest(xmlData) {
    // Implement XML digital signature
    // This is a simplified version - use proper XML-DSig library in production
    const hash = crypto.createHash('sha256').update(xmlData).digest();
    const signature = crypto.sign('RSA-SHA256', hash, this.auaPrivateKey);
    
    // Add signature to XML (simplified)
    return xmlData; // In production, add proper XML signature block
  }

  /**
   * Send request to UIDAI authentication service
   */
  async sendToUidai(xmlData) {
    try {
      const response = await axios.post(this.config.api.baseUrl, xmlData, {
        headers: {
          'Content-Type': 'application/xml',
          'User-Agent': 'AUA-Client/2.5'
        },
        timeout: this.config.api.timeout || 30000
      });

      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`UIDAI API error: ${error.response.status} - ${error.response.data}`);
      } else if (error.request) {
        throw new Error('Network error: Unable to reach UIDAI servers');
      } else {
        throw new Error(`Request error: ${error.message}`);
      }
    }
  }

  /**
   * Parse authentication response
   */
  parseAuthResponse(responseXml) {
    // Parse XML response (use proper XML parser in production)
    const retMatch = responseXml.match(/ret="([^"]+)"/);
    const codeMatch = responseXml.match(/code="([^"]+)"/);
    const errMatch = responseXml.match(/err="([^"]+)"/);
    const txnMatch = responseXml.match(/txn="([^"]+)"/);

    return {
      ret: retMatch ? retMatch[1] : null,
      code: codeMatch ? codeMatch[1] : null,
      err: errMatch ? errMatch[1] : null,
      txn: txnMatch ? txnMatch[1] : null,
      timestamp: new Date().toISOString(),
      success: retMatch && retMatch[1] === 'y'
    };
  }

  // Helper methods
  escapeXml(text) {
    return text.replace(/[<>&'"]/g, (char) => {
      switch (char) {
        case '<': return '&lt;';
        case '>': return '&gt;';
        case '&': return '&amp;';
        case "'": return '&apos;';
        case '"': return '&quot;';
        default: return char;
      }
    });
  }

  generateTransactionId() {
    return 'TXN' + Date.now() + Math.random().toString(36).substr(2, 9);
  }

  getCertificateIdentifier() {
    // Extract certificate identifier from AUA certificate
    return '20240101'; // Placeholder - extract from actual certificate
  }
}

// Usage example
async function example() {
  const config = {
    api: {
      baseUrl: 'https://developer.uidai.gov.in/auth/2.5/public/auth',
      timeout: 30000
    },
    certificates: {
      auaCertPath: './certs/aua-cert.pem',
      auaPrivateKeyPath: './certs/aua-private.key',
      uidaiPublicKeyPath: './certs/uidai-public.pem'
    },
    auaCode: 'AUA_CODE',
    subAuaCode: 'SUB_AUA_CODE'
  };

  const authService = new DemographicAuthService(config);

  const authRequest = {
    uid: '123456789012',
    pid: {
      demo: {
        pi: {
          name: 'John Doe',
          dob: '1990-01-15',
          gender: 'M'
        },
        pa: {
          co: 'IN',
          state: 'Karnataka',
          dist: 'Bangalore Urban',
          pc: '560001'
        }
      }
    }
  };

  try {
    const result = await authService.authenticateDemographic(authRequest);
    console.log('Authentication result:', result);
  } catch (error) {
    console.error('Authentication failed:', error.message);
  }
}

module.exports = DemographicAuthService;
