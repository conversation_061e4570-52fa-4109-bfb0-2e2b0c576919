<Window x:Class="IISLogAnalyzer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="IIS Log Analyzer" Height="800" Width="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Top Panel -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <Button Name="BrowseButton" Content="Browse Log Files" Width="120" Height="30" Click="BrowseButton_Click"/>
            <TextBox Name="FilePathTextBox" Width="400" Height="30" Margin="10,0" IsReadOnly="True"/>
            <Button Name="AnalyzeButton" Content="Analyze" Width="80" Height="30" Click="AnalyzeButton_Click" IsEnabled="False"/>
            <ProgressBar Name="ProgressBar" Width="200" Height="20" Margin="10,0" Visibility="Collapsed"/>
        </StackPanel>
        
        <!-- Filter Panel -->
        <Expander Grid.Row="1" Header="Filters" Margin="10,0" IsExpanded="False">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <StackPanel Grid.Column="0" Margin="5">
                    <Label Content="Start Date:"/>
                    <DatePicker Name="StartDatePicker" Width="120"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="5">
                    <Label Content="End Date:"/>
                    <DatePicker Name="EndDatePicker" Width="120"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="5">
                    <Label Content="IP Address:"/>
                    <TextBox Name="IPFilterTextBox" Width="120"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Margin="5">
                    <Label Content="Status Code:"/>
                    <ComboBox Name="StatusCodeComboBox" Width="120">
                        <ComboBoxItem Content="All"/>
                        <ComboBoxItem Content="200"/>
                        <ComboBoxItem Content="404"/>
                        <ComboBoxItem Content="500"/>
                    </ComboBox>
                </StackPanel>
                
                <Button Grid.Row="1" Grid.Column="0" Name="ApplyFiltersButton" Content="Apply Filters" Width="100" Height="30" Click="ApplyFilters_Click" IsEnabled="False"/>
                <Button Grid.Row="1" Grid.Column="1" Name="ClearFiltersButton" Content="Clear Filters" Width="100" Height="30" Click="ClearFilters_Click"/>
                <CheckBox Grid.Row="1" Grid.Column="2" Name="RealtimeCheckBox" Content="Real-time Monitoring" Checked="RealtimeMonitoring_Checked" Unchecked="RealtimeMonitoring_Unchecked"/>
            </Grid>
        </Expander>
        
        <!-- Main Content -->
        <TabControl Grid.Row="2" Name="MainTabControl" Margin="10">
            <TabItem Header="Traffic Analysis">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <lvc:CartesianChart Name="TrafficChart" Grid.Column="0" Margin="5"/>
                    <DataGrid Name="TrafficGrid" Grid.Column="1" Margin="5" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
            
            <TabItem Header="Performance Analysis">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <lvc:CartesianChart Name="StatusChart" Grid.Row="0" Margin="5"/>
                    <DataGrid Name="SlowRequestsGrid" Grid.Row="1" Margin="5" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
            
            <TabItem Header="Security Analysis">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <DataGrid Name="SuspiciousIPsGrid" Grid.Column="0" Margin="5" AutoGenerateColumns="True"/>
                    <DataGrid Name="ErrorsGrid" Grid.Column="1" Margin="5" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
            
            <TabItem Header="User Behavior">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <lvc:PieChart Name="TopPagesChart" Grid.Column="0" Margin="5"/>
                    <DataGrid Name="TopIPsGrid" Grid.Column="1" Margin="5" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
            
            <TabItem Header="Bandwidth Analysis">
                <lvc:CartesianChart Name="BandwidthChart" Margin="5"/>
            </TabItem>
            
            <TabItem Header="Advanced Security">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <DataGrid Name="BotDetectionGrid" Grid.Row="0" Grid.Column="0" Margin="5" AutoGenerateColumns="True"/>
                    <DataGrid Name="AttackPatternsGrid" Grid.Row="0" Grid.Column="1" Margin="5" AutoGenerateColumns="True"/>
                    <lvc:PieChart Name="ResponseTimeChart" Grid.Row="1" Grid.Column="0" Margin="5"/>
                    <DataGrid Name="SessionAnalysisGrid" Grid.Row="1" Grid.Column="1" Margin="5" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
            
            <TabItem Header="Real-time Alerts">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="Active Alerts" FontSize="16" FontWeight="Bold" Margin="10"/>
                    <DataGrid Name="AlertsGrid" Grid.Row="1" Margin="10" AutoGenerateColumns="True"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- Bottom Panel -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="10" HorizontalAlignment="Right">
            <Button Name="ExportPdfButton" Content="Export PDF" Width="100" Height="30" Click="ExportPdf_Click" IsEnabled="False"/>
            <Button Name="ExportExcelButton" Content="Export Excel" Width="100" Height="30" Margin="5,0" Click="ExportExcel_Click" IsEnabled="False"/>
            <Button Name="ExportCsvButton" Content="Export CSV" Width="100" Height="30" Click="ExportCsv_Click" IsEnabled="False"/>
        </StackPanel>
    </Grid>
</Window>