using System;

namespace IISLogAnalyzer.Models
{
    public class LogEntry
    {
        public DateTime DateTime { get; set; }
        public string ClientIP { get; set; }
        public string Method { get; set; }
        public string UriStem { get; set; }
        public string UriQuery { get; set; }
        public int StatusCode { get; set; }
        public long BytesSent { get; set; }
        public long BytesReceived { get; set; }
        public int TimeTaken { get; set; }
        public string UserAgent { get; set; }
        public string Referer { get; set; }
        public string Username { get; set; }
    }
}