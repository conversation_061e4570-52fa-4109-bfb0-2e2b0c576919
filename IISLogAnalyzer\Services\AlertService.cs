using System;
using System.Collections.Generic;
using System.Linq;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    public class AlertService
    {
        public class Alert
        {
            public string Type { get; set; }
            public string Message { get; set; }
            public DateTime Timestamp { get; set; }
            public string Severity { get; set; }
        }
        
        public List<Alert> CheckAlerts(List<LogEntry> entries)
        {
            var alerts = new List<Alert>();
            var now = DateTime.Now;
            var recentEntries = entries.Where(e => e.DateTime > now.AddMinutes(-5)).ToList();
            
            // High error rate
            var errorRate = recentEntries.Count(e => e.StatusCode >= 400) * 100.0 / Math.Max(1, recentEntries.Count);
            if (errorRate > 10)
            {
                alerts.Add(new Alert
                {
                    Type = "High Error Rate",
                    Message = $"Error rate is {errorRate:F1}% in last 5 minutes",
                    Timestamp = now,
                    Severity = "High"
                });
            }
            
            // Slow response times
            var avgResponseTime = recentEntries.Average(e => e.TimeTaken);
            if (avgResponseTime > 5000)
            {
                alerts.Add(new Alert
                {
                    Type = "Slow Response",
                    Message = $"Average response time is {avgResponseTime:F0}ms",
                    Timestamp = now,
                    Severity = "Medium"
                });
            }
            
            // Suspicious activity
            var suspiciousIPs = recentEntries.GroupBy(e => e.ClientIP)
                                           .Where(g => g.Count() > 100)
                                           .Select(g => g.Key);
            
            foreach (var ip in suspiciousIPs)
            {
                alerts.Add(new Alert
                {
                    Type = "Suspicious Activity",
                    Message = $"High request volume from IP: {ip}",
                    Timestamp = now,
                    Severity = "High"
                });
            }
            
            return alerts;
        }
    }
}