using System;
using System.Windows.Forms;

namespace SampleWindowsApp
{
    public partial class MainForm : Form
    {
        private Button button1;
        private Label label1;

        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.button1 = new Button();
            this.label1 = new Label();
            this.SuspendLayout();

            // button1
            this.button1.Location = new System.Drawing.Point(100, 80);
            this.button1.Size = new System.Drawing.Size(100, 30);
            this.button1.Text = "Click Me";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new EventHandler(this.button1_Click);

            // label1
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(100, 40);
            this.label1.Size = new System.Drawing.Size(100, 15);
            this.label1.Text = "Hello, World!";

            // MainForm
            this.ClientSize = new System.Drawing.Size(300, 200);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.label1);
            this.Text = "Sample Windows App";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Button clicked!");
        }
    }
}