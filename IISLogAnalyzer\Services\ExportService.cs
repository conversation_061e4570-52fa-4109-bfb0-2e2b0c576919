using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using CsvHelper;
using System.Globalization;
using iTextSharp.text;
using iTextSharp.text.pdf;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    public class ExportService
    {
        public void ExportToExcel(Dictionary<string, object> data, string filePath)
        {
            using var workbook = new XLWorkbook();
            
            foreach (var item in data)
            {
                var worksheet = workbook.Worksheets.Add(item.Key);
                
                if (item.Value is Dictionary<string, int> dict)
                {
                    worksheet.Cell(1, 1).Value = "Key";
                    worksheet.Cell(1, 2).Value = "Value";
                    
                    int row = 2;
                    foreach (var kvp in dict)
                    {
                        worksheet.Cell(row, 1).Value = kvp.Key;
                        worksheet.Cell(row, 2).Value = kvp.Value;
                        row++;
                    }
                }
            }
            
            workbook.SaveAs(filePath);
        }

        public void ExportToCsv(Dictionary<string, int> data, string filePath)
        {
            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            
            csv.WriteHeader<KeyValuePair<string, int>>();
            csv.NextRecord();
            
            foreach (var item in data)
            {
                csv.WriteRecord(item);
                csv.NextRecord();
            }
            
            File.WriteAllText(filePath, writer.ToString());
        }

        public void ExportToPdf(Dictionary<string, object> data, string filePath)
        {
            using var document = new Document();
            using var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
            
            document.Open();
            document.Add(new Paragraph("IIS Log Analysis Report"));
            document.Add(new Paragraph($"Generated: {DateTime.Now}"));
            document.Add(new Paragraph(" "));
            
            foreach (var item in data)
            {
                document.Add(new Paragraph(item.Key));
                
                if (item.Value is Dictionary<string, int> dict)
                {
                    var table = new PdfPTable(2);
                    table.AddCell("Key");
                    table.AddCell("Value");
                    
                    foreach (var kvp in dict.Take(10))
                    {
                        table.AddCell(kvp.Key);
                        table.AddCell(kvp.Value.ToString());
                    }
                    
                    document.Add(table);
                }
                
                document.Add(new Paragraph(" "));
            }
            
            document.Close();
        }
    }
}