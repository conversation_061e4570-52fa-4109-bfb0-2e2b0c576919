# AADHAAR Authentication API Reference

## Table of Contents
- [Authentication Methods](#authentication-methods)
- [API Endpoints](#api-endpoints)
- [Request Structure](#request-structure)
- [Response Structure](#response-structure)
- [Data Types](#data-types)
- [XML Schemas](#xml-schemas)

## Authentication Methods

### Supported Authentication Types

| Method | Code | Description | Use Case |
|--------|------|-------------|----------|
| **Biometric** | `bio` | Fingerprint, Iris, Face | High security authentication |
| **OTP** | `otp` | SMS/Email OTP | Mobile/remote authentication |
| **Demographic** | `demo` | Name, DOB, Gender, Address | Basic identity verification |
| **eKYC** | `ekyc` | Full demographic data retrieval | Customer onboarding |

### Authentication Combinations
```xml
<!-- Example: Biometric + Demographic -->
<Uses pi="y" pa="y" bio="y" bt="FMR"/>

<!-- Example: OTP only -->
<Uses otp="y"/>

<!-- Example: Full eKYC -->
<Uses pi="y" pa="y" pfa="y" bio="y" ekyc="y"/>
```

## API Endpoints

### 1. Authentication Endpoint
**Endpoint**: `POST /auth`
**Purpose**: Primary authentication endpoint for all auth types

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `uid` | String | Yes | 12-digit Aadhaar number |
| `tid` | String | Yes | Transaction ID |
| `ac` | String | Yes | AUA code |
| `sa` | String | Yes | Sub-AUA code |
| `ver` | String | Yes | API version (2.5) |
| `txn` | String | Yes | Transaction reference |
| `ts` | DateTime | Yes | Timestamp (ISO 8601) |

#### Request Headers
```http
Content-Type: application/xml
Authorization: Bearer <token>
X-Request-ID: <unique-request-id>
```

#### Sample Request Structure
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Auth uid="123456789012" tid="AUA:TXN123" ac="AUA_CODE" sa="SA_CODE" 
      ver="2.5" txn="TXN123456" ts="2024-01-15T10:30:00">
  
  <!-- Authentication methods to use -->
  <Uses pi="y" pa="y" bio="y" bt="FMR" otp="n"/>
  
  <!-- Metadata -->
  <Meta udc="public" fdc="NC" idc="NA" pip="127.0.0.1" lot="P" lov="560001"/>
  
  <!-- Session key -->
  <Skey ci="20231215">ENCRYPTED_SESSION_KEY</Skey>
  
  <!-- Encrypted PID data -->
  <Data type="X">ENCRYPTED_PID_BLOCK</Data>
  
  <!-- HMAC for integrity -->
  <Hmac>HMAC_VALUE</Hmac>
  
</Auth>
```

### 2. eKYC Endpoint
**Endpoint**: `POST /ekyc`
**Purpose**: Retrieve demographic and biometric data

#### Additional Parameters for eKYC
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `lr` | String | No | Language for response |
| `de` | String | No | Data elements to retrieve |

### 3. OTP Request Endpoint
**Endpoint**: `POST /otp`
**Purpose**: Generate OTP for authentication

#### OTP Request Structure
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Otp uid="123456789012" tid="AUA:OTP123" ac="AUA_CODE" sa="SA_CODE" 
     ver="2.5" ts="2024-01-15T10:30:00" type="A" ch="01">
  
  <Opts ws="Y"/>
  
</Otp>
```

## Request Structure

### Core Elements

#### 1. Auth Element (Root)
```xml
<Auth uid="" tid="" ac="" sa="" ver="" txn="" ts="">
  <!-- Child elements -->
</Auth>
```

#### 2. Uses Element
Specifies which authentication factors to use:
```xml
<Uses pi="y|n" pa="y|n" pfa="y|n" bio="y|n" bt="FMR|FIR|IIR" otp="y|n" ekyc="y|n"/>
```

**Attributes:**
- `pi`: Personal Identity (Name, DOB, Gender)
- `pa`: Personal Address
- `pfa`: Personal Full Address
- `bio`: Biometric data
- `bt`: Biometric type (FMR=Fingerprint, FIR=Finger Image, IIR=Iris Image)
- `otp`: One Time Password
- `ekyc`: Electronic Know Your Customer

#### 3. Meta Element
```xml
<Meta udc="" fdc="" idc="" pip="" lot="" lov=""/>
```

**Attributes:**
- `udc`: User device code
- `fdc`: Finger device code  
- `idc`: Iris device code
- `pip`: Public IP address
- `lot`: Location type (P=Pincode, G=GPS)
- `lov`: Location value

#### 4. Skey Element
```xml
<Skey ci="certificate_identifier">ENCRYPTED_SESSION_KEY</Skey>
```

#### 5. Data Element
```xml
<Data type="X">ENCRYPTED_PID_BLOCK</Data>
```

#### 6. Hmac Element
```xml
<Hmac>HMAC_SHA256_VALUE</Hmac>
```

## Response Structure

### Successful Authentication Response
```xml
<?xml version="1.0" encoding="UTF-8"?>
<AuthRes ret="y" code="0000" txn="TXN123456" ts="2024-01-15T10:30:05" 
         err="" info="">
  
  <!-- Optional eKYC data -->
  <UidData>
    <Poa co="IN" country="India" dist="Bangalore Urban" 
         house="123" lm="Near Park" loc="Koramangala" 
         pc="560034" po="Koramangala" state="Karnataka" 
         street="5th Block" subdist="Bangalore South" vtc="Bangalore"/>
    
    <Poi dob="1990-01-15" dobType="V" gender="M" name="John Doe"/>
    
    <Pht>BASE64_ENCODED_PHOTO</Pht>
  </UidData>
  
</AuthRes>
```

### Error Response
```xml
<?xml version="1.0" encoding="UTF-8"?>
<AuthRes ret="n" code="300" txn="TXN123456" ts="2024-01-15T10:30:05" 
         err="Invalid UID" info="The UID provided is not valid"/>
```

## Data Types

### Standard Data Types
| Type | Format | Example | Description |
|------|--------|---------|-------------|
| UID | 12 digits | `123456789012` | Aadhaar number |
| Timestamp | ISO 8601 | `2024-01-15T10:30:00` | UTC timestamp |
| Transaction ID | String | `AUA:TXN123` | Unique transaction identifier |
| Version | String | `2.5` | API version |
| Boolean | y/n | `y` | Yes/No values |

### Biometric Data Types
| Type | Description | Format |
|------|-------------|--------|
| FMR | Fingerprint Minutiae | ISO 19794-2 |
| FIR | Fingerprint Image | WSQ/JPEG2000 |
| IIR | Iris Image | ISO 19794-6 |
| FACE | Face Image | JPEG |

## XML Schemas

### PID (Personal Identity Data) Schema
```xml
<Pid ts="" ver="">
  <Demo lang="">
    <Pi ms="" mv="" name="" lname=""/>
    <Pa ms="" mv="" co="" dist="" house="" lm="" loc="" 
        pc="" po="" state="" street="" subdist="" vtc=""/>
    <Pfa ms="" mv="" av=""/>
  </Demo>
  
  <Bios>
    <Bio type="" posh="" bs="">BIOMETRIC_DATA</Bio>
  </Bios>
  
  <Pv otp="" pin=""/>
</Pid>
```

---

**Next Sections**: Continue with detailed schema definitions, validation rules, and implementation examples.
