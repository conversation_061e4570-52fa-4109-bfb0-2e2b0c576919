using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    public class RealtimeMonitor
    {
        private FileSystemWatcher _watcher;
        private readonly LogParser _parser;
        private long _lastPosition;
        
        public event Action<List<LogEntry>> NewEntriesDetected;
        
        public RealtimeMonitor()
        {
            _parser = new LogParser();
        }
        
        public void StartMonitoring(string logFilePath)
        {
            var directory = Path.GetDirectoryName(logFilePath);
            var fileName = Path.GetFileName(logFilePath);
            
            _watcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.Size | NotifyFilters.LastWrite
            };
            
            _watcher.Changed += OnLogFileChanged;
            _watcher.EnableRaisingEvents = true;
            
            _lastPosition = new FileInfo(logFilePath).Length;
        }
        
        private async void OnLogFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                await Task.Delay(100); // Wait for write to complete
                
                var newEntries = await ReadNewEntries(e.FullPath);
                if (newEntries.Any())
                {
                    NewEntriesDetected?.Invoke(newEntries);
                }
            }
            catch (Exception ex)
            {
                // Log error
            }
        }
        
        private async Task<List<LogEntry>> ReadNewEntries(string filePath)
        {
            var entries = new List<LogEntry>();
            
            using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            stream.Seek(_lastPosition, SeekOrigin.Begin);
            
            using var reader = new StreamReader(stream);
            string line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (!line.StartsWith("#") && !string.IsNullOrWhiteSpace(line))
                {
                    // Parse new log entry
                    var entry = await _parser.ParseLogFileAsync(filePath);
                    if (entry.Any())
                        entries.AddRange(entry.TakeLast(1));
                }
            }
            
            _lastPosition = stream.Position;
            return entries;
        }
        
        public void StopMonitoring()
        {
            _watcher?.Dispose();
        }
    }
}