{"info": {"name": "Aadhaar Authentication API v2.5", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "aadhaar-authentication-api-v25"}, "item": [{"name": "Authenticate <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<Auth uid=\"************\" rc=\"Y\" tid=\"registered\" ac=\"public\" sa=\"public\" ver=\"2.5\" txn=\"TestTxn001\" lk=\"test_license_key\">\n    <Uses pi=\"y\" pa=\"n\" pfa=\"n\" bio=\"n\" bt=\"\" pin=\"n\" otp=\"y\"/>\n    <Device rdsId=\"\" rdsVer=\"\" dpId=\"\" dc=\"\" mi=\"\" mc=\"\"/>\n    <Skey ci=\"20220101\">ENC_SESSION_KEY</Skey>\n    <Hmac>ENC_HASH</Hmac>\n    <Data type=\"X\">ENC_PID_BLOCK</Data>\n    <Signature>Digital_Signature_Here</Signature>\n</Auth>"}, "url": {"raw": "https://auth.uidai.gov.in/2.5/public/0/0/test_asalk", "protocol": "https", "host": ["auth", "uidai", "gov", "in"], "path": ["2.5", "public", "0", "0", "test_asalk"]}}, "response": []}]}