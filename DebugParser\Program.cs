using System;
using System.IO;

class Program
{
    static void Main()
    {
        var lines = File.ReadAllLines("../u_ex250722.log");
        string[] fieldNames = null;
        
        Console.WriteLine($"Total lines: {lines.Length}");
        
        foreach (var line in lines)
        {
            if (line.StartsWith("#Fields:"))
            {
                fieldNames = line.Substring(8).Split(' ');
                Console.WriteLine($"Fields found: {string.Join(", ", fieldNames)}");
                continue;
            }
            
            if (line.StartsWith("#") || string.IsNullOrWhiteSpace(line))
                continue;
                
            var fields = line.Split(' ');
            Console.WriteLine($"Data line: {fields.Length} fields");
            Console.WriteLine($"Line: {line}");
            
            if (fieldNames != null)
            {
                for (int i = 0; i < Math.Min(fields.Length, fieldNames.Length); i++)
                {
                    Console.WriteLine($"  {fieldNames[i]} = {fields[i]}");
                }
            }
            
            break; // Just check first data line
        }
        
        Console.ReadKey();
    }
}